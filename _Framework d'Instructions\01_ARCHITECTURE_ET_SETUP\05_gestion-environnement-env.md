Gestion des Variables d'Environnement (.env)

**Objectif :** Ce document définit les procédures pour gérer les variables d'environnement de manière sécurisée et efficace, notamment pour les clés API et les configurations sensibles.

**Directives pour l'agent de codage :**

*   **Sécurité des informations :** Soyez extrêmement vigilant quant à la manipulation des variables d'environnement. Les clés sensibles ne doivent **jamais** être commises directement dans le dépôt de code.
*   **Bonnes pratiques :** Référez-vous aux meilleures pratiques de gestion des `.env` dans les applications React/Next.js. Context7 pourrait potentiellement fournir des informations sur les configurations standardisées pour différents frameworks (ex: `NEXT_PUBLIC_` pour Next.js).
*   **Augment Code pour la détection :** Augment Code peut vous aider à identifier les variables d'environnement dans le code et suggérer des améliorations pour leur gestion ou leur obfuscation si nécessaire, car il comprend la codebase.

Ce fichier abordera la création du fichier `.env.local`, l'intégration des variables dans le code de l'application et les meilleures pratiques de sécurité.
