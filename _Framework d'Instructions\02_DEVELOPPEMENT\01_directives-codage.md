# 01_directives-codage.md : Directives Générales de Codage

**Objectif :** Ce document fournit des lignes directrices générales pour l'agent de codage concernant l'implémentation du code, l'utilisation des outils IA et la gestion des tâches de développement.

**Directives pour l'agent de codage :**

1.  **Cohérence avec l'architecture :** Chaque morceau de code généré doit être en parfaite adéquation avec l'architecture définie dans le chapitre `01_ARCHITECTURE_ET_SETUP`. Référez-vous aux documents pertinents (ex: `02_structure-composants-react.md`, `03_configuration-tailwind.md`, `04_setup-firebase.md`) pour les conventions et les structures.
2.  **Utilisation d'Augment Code :**
    *   Augment Code est votre plateforme IA principale pour la productivité. Utilisez ses fonctionnalités de **Chat** pour poser des questions ou obtenir des clarifications sur des concepts.
    *   Employez **Next Edit** pour les refactorisations, les modifications complexes ou les tâches répétitives, en suivant ses suggestions étape par étape.
    *   Utilisez les **Code Completions** pour accélérer l'écriture de code, en vous appuyant sur sa compréhension de la base de code existante.
3.  **Appel aux serveurs MCP (Master Control Program) :**
    *   Pour toute question nécessitant une documentation technique à jour ou des exemples de code précis pour des bibliothèques spécifiques (React, Firebase, Tailwind, etc.), utilisez la directive `use context7` (si vous opérez dans un client MCP compatible tel que Cursor, Windsurf, Claude Desktop, Augment Code, JetBrains AI Assistant ou VS Code). Cela permettra d'éviter les informations obsolètes ou les APIs inexistantes.
    *   Si vous avez la capacité via un accès web, **recherchez activement d'autres serveurs MCP** spécialisés dans les technologies de notre stack si Context7 ne fournit pas l'information requise ou si une alternative plus pertinente existe.
    *   Vous pouvez proposer l'intégration de bibliothèques fiables découvertes via ces MCP, mais toute installation nécessitera une approbation.
4.  **Clarté et maintenabilité du code :** Écrivez du code lisible, bien commenté et conforme aux meilleures pratiques de l'industrie (ex: principes SOLID, DRY, KISS). Priorisez la simplicité et l'efficacité.
5.  **Gestion des erreurs :** Implémentez une gestion d'erreurs robuste pour toutes les opérations potentiellement échouées (appels API, interactions utilisateur, etc.).
6.  **Sécurité :** Soyez conscient des implications de sécurité, notamment lors de la manipulation de données sensibles ou d'interactions avec des services externes. Ne jamais coder en dur des identifiants ou des clés API.
7.  **Auto-correction et vérification :** Avant de considérer une tâche comme terminée, effectuez une auto-vérification approfondie de votre code. Vérifiez qu'il répond aux exigences, qu'il est fonctionnel et qu'il n'introduit pas de régressions.
8.  **Documentation :** Écrivez des commentaires et des notes pour aider les autres à comprendre votre code. Cela peut inclure des explications techniques, des exemples de code, des étapes de configuration et des considérations de performance.

---

## **CONSTELLATION D'OUTILS ET MODÈLES IA - RÉFÉRENCE ÉTENDUE**

### **LLM & Agents Conversationnels**

*De nombreux modèles, en particulier open-source, sont accessibles et testables via des plateformes d'agrégation comme **OpenRouter**, **Hugging Face** ou **Poe**.*

| Nom de l'outil / Modèle | Lien Officiel / Info | Description |
| :---- | :---- | :---- |
| **ChatGPT (Famille GPT)** | [https://chat.openai.com](https://chat.openai.com) | Le plus célèbre des agents conversationnels, basé sur les modèles GPT-3.5 et GPT-4o. |
| **Gemini (Famille Google)** | [https://gemini.google.com](https://gemini.google.com) | La famille de modèles multimodaux de Google (Pro, Flash, Advanced). |
| **Claude (Famille Anthropic)** | [https://claude.ai](https://claude.ai) | Connu pour sa grande fenêtre de contexte et son approche éthique (Sonnet, Opus). |
| **Llama (Famille Meta)** | [https://ai.meta.com/llama/](https://www.google.com/search?q=https://ai.meta.com/llama/) | Famille de modèles open-source très performants, développée par Meta. |
| **Mistral (Famille Mistral AI)** | [https://mistral.ai/](https://mistral.ai/) | Modèles européens (7B, 8x7B, Large) réputés pour leur efficacité. |
| **Le Chat (Mistral AI)** | [https://chat.mistral.ai](https://chat.mistral.ai) | L'interface de chat officielle pour tester les modèles de Mistral AI. |
| **Perplexity AI** | [https://www.perplexity.ai](https://www.perplexity.ai) | Un "moteur de réponse" conversationnel qui cite ses sources. |
| **Grok (xAI)** | [https://x.ai/grok](https://x.ai/grok) | Le modèle d'Elon Musk, intégré à X (Twitter), avec un ton plus audacieux. |
| **Cohere (Famille Command)** | [https://cohere.com/](https://cohere.com/) | Modèles (Command R/R+) axés sur les cas d'usage en entreprise. |
| **Hugging Chat** | [https://huggingface.co/chat](https://huggingface.co/chat) | L'interface de chat de la plateforme open-source Hugging Face. |
| **Kimi (Moonshot AI)** | [https://kimi.moonshot.cn/](https://kimi.moonshot.cn/) | Modèle chinois réputé pour sa très grande fenêtre de contexte (200k tokens). |
| **Yi (01.AI)** | [https://01.ai/](https://01.ai/) | Modèle bilingue (EN/CN) très performant développé par l'ancien CEO de Microsoft. |
| **Jasper** | [https://www.jasper.ai](https://www.jasper.ai) | Un des leaders de la génération de contenu marketing et copywriting. |
| **Copy.ai** | [https://www.copy.ai](https://www.copy.ai) | Plateforme spécialisée dans la rédaction de textes pour les ventes et le marketing. |
| **You.com** | [https://you.com](https://you.com) | Moteur de recherche conversationnel personnalisable. |

### **Génération d'Images**

| Nom de l'outil | Lien Officiel | Description |
| :---- | :---- | :---- |
| **Midjourney** | [https://www.midjourney.com](https://www.midjourney.com) | L'un des générateurs d'images les plus puissants et artistiques (via Discord). |
| **DALL-E 3 (OpenAI)** | [https://openai.com/dall-e-3](https://openai.com/dall-e-3) | Le générateur d'images d'OpenAI, intégré à ChatGPT Plus et Microsoft Copilot. |
| **Adobe Firefly** | [https://www.adobe.com/sensei/generative-ai/firefly.html](https://www.adobe.com/sensei/generative-ai/firefly.html) | La suite de modèles d'IA générative d'Adobe, intégrée dans Photoshop et Illustrator. |
| **Freepik AI** | [https://www.freepik.com/ai](https://www.freepik.com/ai) | Générateur d'images intégré à la célèbre banque d'images. |

### **Génération de Vidéo & Musique**

| Nom de l'outil | Lien Officiel | Description |
| :---- | :---- | :---- |
| **Suno** | [https://suno.com](https://suno.com) | Crée des chansons complètes (paroles, instruments, voix) à partir d'un prompt. |
| **Pika** | [https://pika.art](https://pika.art) | Un outil puissant pour générer et éditer des vidéos par IA. |
| **RunwayML** | [https://runwayml.com](https://runwayml.com) | Suite d'outils d'édition vidéo IA, incluant la génération texte-vidéo (Gen-2). |
| **HeyGen** | [https://www.heygen.com](https://www.heygen.com) | Crée des vidéos avec des avatars IA et permet le doublage et la synchronisation labiale. |

### **Assistants de Code & Outils Développeur**

| Nom de l'outil | Lien Officiel | Description |
| :---- | :---- | :---- |
| **GitHub Copilot** | [https://github.com/features/copilot](https://github.com/features/copilot) | L'assistant de code de référence, intégré à de nombreux IDE. |
| **Gemini Code Assist** | [https://cloud.google.com/products/gemini/code-assist](https://cloud.google.com/products/gemini/code-assist) | L'assistant de code de Google pour les entreprises, intégré à l'écosystème Google Cloud. |
| **Augment Code** | [https://www.augmentcode.com/](https://www.augmentcode.com/) | Ton assistant de code IA de prédilection ! |
| **Amazon CodeWhisperer** | [https://aws.amazon.com/codewhisperer/](https://aws.amazon.com/codewhisperer/) | L'assistant de code d'Amazon, gratuit pour les développeurs individuels. |
| **Tabnine** | [https://www.tabnine.com](https://www.tabnine.com) | Assistant de code IA qui s'adapte à votre style et vos projets. |
| **CodiumAI** | [https://www.codium.ai/](https://www.codium.ai/) | Spécialisé dans la génération de tests unitaires et l'analyse de code. |
| **Code Llama (Meta)** | [https://ai.meta.com/blog/code-llama/](https://www.google.com/search?q=https://ai.meta.com/blog/code-llama/) | Modèle open-source spécialisé dans la génération de code, par Meta. |
| **Refact.ai** | [https://refact.ai](https://refact.ai) | Un autre assistant de code puissant avec des fonctionnalités de refactoring. |
| **LiteLLM** | [https://www.litellm.ai](https://www.litellm.ai) | Outil développeur qui simplifie l'appel à différentes API de LLM. |
| **OpenRouter** | [https://openrouter.ai](https://openrouter.ai) | Agrège de nombreux modèles LLM (y compris des modèles non censurés) via une seule API. |

### **Productivité & Automatisation**

| Nom de l'outil | Lien Officiel | Description |
| :---- | :---- | :---- |
| **Notion AI** | [https://www.notion.so/product/ai](https://www.google.com/search?q=https://www.notion.so/product/ai) | Assistant IA intégré à l'espace de travail Notion pour résumer, rédiger, etc. |
| **Fireflies.ai** | [https://fireflies.ai](https://fireflies.ai) | Transcrit et résume automatiquement les réunions en ligne. |
| **Aqua Voice** | [https://www.aquavoice.app/](https://www.google.com/search?q=https://www.aquavoice.app/) | Outil de dictée rapide par IA pour prendre des notes vocales n'importe où. |
| **Abacus.ai** | [https://abacus.ai/](https://abacus.ai/) | Plateforme MLOps et d'IA générative pour les entreprises. |

### **Design & Présentations**

| Nom de l'outil | Lien Officiel | Description |
| :---- | :---- | :---- |
| **Gamma** | [https://gamma.app](https://gamma.app) | Crée des présentations, documents et pages web à partir d'un simple prompt. |
| **Tome** | [https://tome.app](https://tome.app) | Plateforme de "storytelling" IA pour générer des présentations narratives et visuelles. |
| **Uizard** | [https://uizard.io](https://uizard.io) | Outil de design UI qui transforme des prompts texte ou des croquis en maquettes. |
| **MindStudio** | [https://www.mindstudio.ai](https://www.mindstudio.ai) | Plateforme No-Code pour créer des applications basées sur l'IA sans coder. |