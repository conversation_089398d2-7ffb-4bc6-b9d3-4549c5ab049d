# **04_ANALYSE-TECHNIQUE.MD - ÉVALUATION DE FAISABILITÉ TECHNIQUE**

## **OBJECTIF DE L'ANALYSE TECHNIQUE**

Ce document guide l'agent dans l'évaluation de la faisabilité technique du projet et la définition de l'architecture appropriée, en s'appuyant sur la stack technologique autorisée.

---

## **1. ÉVALUATION DE LA STACK TECHNOLOGIQUE**

### **1.1 Conformité avec la Stack Autorisée**

**Vérification obligatoire :**
- ✅ **React 18+** : Adapté aux besoins du projet
- ✅ **TypeScript** : Complexité du typage évaluée
- ✅ **Vite.js** : Performance de build appropriée
- ✅ **Tailwind CSS** : Suffisant pour les besoins de design
- ✅ **shadcn/ui** : Composants UI disponibles pour les besoins
- ✅ **GSAP** : Animations requises identifiées
- ✅ **Three.js/R3F** : Besoins 3D évalués
- ✅ **Netlify** : Hébergement adapté aux exigences

### **1.2 Analyse des Limitations**

**Points de vigilance à documenter :**
- Limitations de performance potentielles
- Contraintes de la stack pour les fonctionnalités demandées
- Besoins de contournements ou d'adaptations
- Risques techniques identifiés

---

## **2. ARCHITECTURE TECHNIQUE PROPOSÉE**

### **2.1 Structure des Composants**

**Définir l'organisation :**
```
src/
├── components/
│   ├── ui/              # Composants shadcn/ui
│   ├── layout/          # Composants de mise en page
│   ├── features/        # Composants métier
│   └── common/          # Composants réutilisables
├── pages/               # Pages de l'application
├── hooks/               # Hooks personnalisés
├── utils/               # Fonctions utilitaires
├── types/               # Types TypeScript
├── services/            # Services API et Firebase
└── assets/              # Ressources statiques
```

### **2.2 Gestion des États**

**Stratégie recommandée :**
- **État local** : `useState`, `useReducer` pour les composants
- **État global** : Context API ou Zustand selon la complexité
- **État serveur** : React Query pour les données Firebase
- **Formulaires** : React Hook Form avec validation Zod

### **2.3 Routing et Navigation**

**Configuration proposée :**
- React Router v6 pour la navigation
- Lazy loading des pages pour les performances
- Protection des routes selon l'authentification
- Gestion des erreurs 404 et redirections

---

## **3. INTÉGRATION FIREBASE**

### **3.1 Services Firebase Nécessaires**

**Évaluation des besoins :**
- [ ] **Authentication** : Types d'auth requis (email, OAuth, anonyme)
- [ ] **Firestore** : Structure des collections et documents
- [ ] **Storage** : Gestion des fichiers et médias
- [ ] **Functions** : Logique serveur nécessaire
- [ ] **Hosting** : Hébergement des assets statiques

### **3.2 Règles de Sécurité**

**Planification obligatoire :**
- Définir les règles Firestore selon les rôles utilisateur
- Planifier les règles Storage pour les fichiers
- Identifier les besoins de validation côté serveur
- Prévoir les tests de sécurité

---

## **4. PERFORMANCE ET OPTIMISATION**

### **4.1 Stratégies d'Optimisation**

**Mesures préventives :**
- Code splitting par routes et fonctionnalités
- Lazy loading des composants lourds
- Optimisation des images (WebP, lazy loading)
- Mise en cache appropriée des données Firebase

### **4.2 Métriques de Performance**

**Objectifs à atteindre :**
- **LCP (Largest Contentful Paint)** : < 2.5s
- **FID (First Input Delay)** : < 100ms
- **CLS (Cumulative Layout Shift)** : < 0.1
- **Bundle size** : < 500KB initial

---

## **5. SÉCURITÉ ET CONFORMITÉ**

### **5.1 Mesures de Sécurité**

**Implémentations obligatoires :**
- Validation et sanitisation des inputs
- Protection CSRF et XSS
- Chiffrement des données sensibles
- Audit des dépendances (npm audit)

### **5.2 Conformité RGPD**

**Si applicable :**
- Consentement pour les cookies
- Droit à l'oubli implémenté
- Minimisation des données collectées
- Logs d'audit des accès

---

## **6. ESTIMATION ET PLANIFICATION**

### **6.1 Complexité Technique**

**Évaluation par fonctionnalité :**
- **Simple** : Composants statiques, pages informatives
- **Modéré** : Formulaires, authentification basique
- **Complexe** : Interactions temps réel, animations avancées
- **Critique** : Logique métier complexe, intégrations externes

### **6.2 Risques Identifiés**

**Documentation des risques :**
- Risques techniques (compatibilité, performance)
- Risques fonctionnels (complexité, ambiguïtés)
- Risques de délais (dépendances externes)
- Plans de mitigation proposés

---

## **7. RECOMMANDATIONS ET ALTERNATIVES**

### **7.1 Optimisations Suggérées**

**Améliorations possibles :**
- Alternatives techniques plus performantes
- Simplifications d'architecture
- Optimisations de développement
- Réductions de complexité

### **7.2 Plan B et Alternatives**

**Solutions de repli :**
- Fonctionnalités dégradées si contraintes techniques
- Alternatives à certaines technologies si problèmes
- Phases de développement modulaires
- Options de déploiement alternatives

---

## **8. VALIDATION ET APPROBATION**

### **8.1 Checklist de Validation Technique**

**Vérifications obligatoires :**
- [ ] Architecture cohérente avec les exigences
- [ ] Stack technologique appropriée et autorisée
- [ ] Faisabilité technique confirmée
- [ ] Risques identifiés et documentés
- [ ] Performance estimée acceptable
- [ ] Sécurité planifiée et suffisante
- [ ] Conformité aux standards du framework

### **8.2 Approbation Cisco**

**Présentation obligatoire :**
1. **Résumé** de l'analyse technique
2. **Architecture** proposée avec justifications
3. **Risques** identifiés et plans de mitigation
4. **Estimation** de complexité et délais
5. **Recommandations** d'optimisation
6. **Demande** d'approbation explicite

---

**⚠️ IMPORTANT :** Cette analyse technique doit être exhaustive et honnête. Tout risque ou limitation non identifié à cette étape peut compromettre le succès du projet. L'agent doit privilégier la transparence et la précision technique.

---

## **9. ARCHITECTURE EXISTANTE FLEXODIV - ANALYSE DÉTAILLÉE**

### **9.1 Stack Technique Actuelle**

**Frontend Core :**
- **React 19.1.0** + **TypeScript 5.7.2** - Framework principal
- **Vite 6.2.0** - Serveur de développement et build
- **React Router DOM 7.6.3** - Navigation SPA
- **Tailwind CSS** - Framework CSS utilitaire

**Intégration IA :**
- **@google/genai 1.9.0** - SDK Google Gemini
- **Modèle** : `gemini-2.5-flash`
- **Base de connaissances** : TypeScript structurée

**Déploiement :**
- **Netlify** - Hébergement et CI/CD
- **Node.js 18** - Environnement de build

### **9.2 Structure des Composants Existante**

```
FlexoDiv-vitrine/
├── components/           # Composants React réutilisables
│   ├── AIChat.tsx          # Assistant IA principal
│   ├── AIChatDebug.tsx     # Version debug de l'IA
│   ├── Header.tsx          # Navigation principale
│   ├── GlowingCard.tsx     # Cartes avec effets lumineux
│   ├── ScrollToTop.tsx     # Auto-scroll sur changement de route
│   └── ScrollToTopButton.tsx # Bouton de remontée
├── pages/               # Pages principales du site
│   ├── Home.tsx           # Page d'accueil
│   ├── About.tsx          # À propos
│   ├── Portfolio.tsx      # Projets
│   ├── Services.tsx       # Compétences
│   └── Contact.tsx        # Contact + IA
├── services/           # Services et logique métier
│   └── geminiService.ts   # Service API Gemini
├── data/              # Données et configuration
│   ├── knowledge-base.ts  # Base de connaissances IA
│   └── README-Assistant.md # Doc assistant IA
├── styles/            # Styles CSS personnalisés
│   ├── glowing-card.css  # Effets lumineux
│   ├── scrollbar-hide.css # Masquage scrollbars
│   └── sticky-header.css  # Header fixe
├── types/             # Définitions TypeScript
│   └── index.ts          # Types globaux
└── public/            # Assets statiques
    └── assets/           # Images, logos, projets
```

### **9.3 Architecture de l'Assistant IA**

**Flux de données IA :**
```
Assistant IA FlexoDiv
├── pages/Contact.tsx
│   └── Importe components/AIChat.tsx
├── components/AIChat.tsx
│   ├── Importe services/geminiService.ts
│   ├── Importe types/index.ts (ChatMessage)
│   ├── Gère l'état des messages
│   ├── Gère l'interface utilisateur
│   └── Appelle streamChatResponse()
├── services/geminiService.ts
│   ├── Importe @google/genai
│   ├── Importe data/knowledge-base.ts
│   ├── Configure l'API Gemini
│   ├── Crée l'instruction système
│   └── Gère le streaming des réponses
└── data/knowledge-base.ts
    ├── Profil de Francisco/FlexoDiv
    ├── Compétences techniques
    ├── Portfolio de projets
    ├── Services et tarifs
    └── Philosophie de développement
```

### **9.4 Système de Styles Existant**

**Configuration Tailwind :**
```
tailwind.config.js
├── Définit les couleurs de marque (brand-*)
├── Configure les breakpoints responsive
├── Étend les utilitaires personnalisés
└── Optimise la purge CSS

Couleurs principales :
├── brand-dark: #1B1C1D (fond principal)
├── brand-surface: #282A2C (surfaces)
├── brand-blue: #2190F6 (accent bleu)
├── brand-purple: #AE87F3 (accent violet)
└── brand-muted: #9CA3AF (texte secondaire)
```

**Styles Personnalisés :**
```
styles/
├── glowing-card.css      # Effets de brillance sur les cartes
├── scrollbar-hide.css    # Masque les scrollbars
└── sticky-header.css     # Comportement header fixe
```

---

## **10. DIAGNOSTIC RAPIDE - CHECKLIST DE VÉRIFICATION**

### **10.1 Vérifications Critiques (30 secondes)**

**1. Site Accessible ?**
```bash
npm run dev
# Doit afficher : Local: http://localhost:5173/
```
✅ **OK** : Site accessible
❌ **KO** : Voir section "Problèmes de Build"

**2. Agent Gemini Fonctionne ?**
```bash
# Vérifier la variable d'environnement
echo $VITE_GEMINI_API_KEY
# Ou dans le fichier .env.local
cat .env.local
```
✅ **OK** : Clé présente
❌ **KO** : Voir section "Problèmes IA"

**3. Navigation Fonctionne ?**
- Cliquer sur chaque lien du menu
- Vérifier que les pages se chargent
- Tester le logo (retour accueil)

✅ **OK** : Toutes les pages accessibles
❌ **KO** : Voir section "Problèmes de Routing"

### **10.2 Diagnostic par Symptôme**

**SYMPTÔME : Page Blanche**
```
CAUSES POSSIBLES :
├── Erreur JavaScript critique
├── Composant manquant dans App.tsx
├── Erreur de syntaxe React
└── Problème d'import

VÉRIFICATIONS :
1. Ouvrir Console Développeur (F12)
2. Regarder les erreurs rouges
3. Vérifier Network tab (ressources manquantes)
4. Vérifier tous les imports dans App.tsx
```

**SYMPTÔME : Agent IA Ne Répond Pas**
```
CAUSES POSSIBLES :
├── VITE_GEMINI_API_KEY manquante/incorrecte
├── Package @google/genai corrompu
├── knowledge-base.ts malformé
├── Quota API dépassé
└── Erreur réseau

VÉRIFICATIONS :
1. Console → Erreurs liées à "gemini" ou "AI"
2. Network tab → Appels API échoués
3. Vérifier .env.local existe et contient la clé
4. Tester avec un message simple
```

**SYMPTÔME : Styles Cassés**
```
CAUSES POSSIBLES :
├── Tailwind CSS non chargé
├── tailwind.config.js corrompu
├── Classes CSS incorrectes
└── Fichiers CSS personnalisés manquants

VÉRIFICATIONS :
1. Vérifier que les classes Tailwind s'appliquent
2. Inspecter les éléments (styles calculés)
3. Vérifier imports CSS dans index.tsx
4. Rebuilder : npm run build
```

### **10.3 Commandes de Diagnostic**

**Vérification Complète :**
```bash
# 1. Vérifier les dépendances
npm list --depth=0

# 2. Vérifier la configuration
cat package.json | grep -A 10 -B 10 "dependencies"

# 3. Vérifier les variables d'environnement
cat .env.local

# 4. Test de build
npm run build

# 5. Vérifier les fichiers critiques
ls -la components/AIChat.tsx
ls -la services/geminiService.ts
ls -la data/knowledge-base.ts
```

### **10.4 Checklist Fichiers Critiques**

**Fichiers Obligatoires :**
- [ ] `index.tsx` - Point d'entrée
- [ ] `App.tsx` - Composant racine
- [ ] `components/AIChat.tsx` - Agent Gemini
- [ ] `services/geminiService.ts` - Service IA
- [ ] `data/knowledge-base.ts` - Base de connaissances
- [ ] `types/index.ts` - Types TypeScript
- [ ] `.env.local` - Variables d'environnement
- [ ] `package.json` - Dépendances
- [ ] `vite.config.ts` - Configuration build
- [ ] `tailwind.config.js` - Configuration styles

**Pages Obligatoires :**
- [ ] `pages/Home.tsx`
- [ ] `pages/About.tsx`
- [ ] `pages/Services.tsx`
- [ ] `pages/Portfolio.tsx`
- [ ] `pages/Contact.tsx`

**Composants Obligatoires :**
- [ ] `components/Header.tsx`
- [ ] `components/ScrollToTop.tsx`
- [ ] `components/ScrollToTopButton.tsx`
- [ ] `components/GlowingCard.tsx` (si utilisé)

### **10.5 Solutions Rapides**

**Problème : Site ne démarre pas**
```bash
# 1. Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install

# 2. Vérifier Node.js version
node --version  # Doit être >= 18

# 3. Redémarrer proprement
npm run dev
```

**Problème : IA ne fonctionne pas**
```bash
# 1. Vérifier/créer .env.local
echo "VITE_GEMINI_API_KEY=AIzaSyCqooMDW5sKHciKAX19JUolQT01RDhBysc" > .env.local

# 2. Redémarrer le serveur (obligatoire après modification .env)
```

### **10.6 Métriques de Santé**

**Performance :**
- [ ] Site se charge en < 3 secondes
- [ ] Agent IA répond en < 5 secondes
- [ ] Navigation fluide sans lag

**Fonctionnalité :**
- [ ] Toutes les pages accessibles
- [ ] Agent IA répond correctement
- [ ] Formulaire de contact fonctionne
- [ ] Responsive sur mobile/tablet

**Technique :**
- [ ] Aucune erreur console
- [ ] Build réussit sans warnings
- [ ] Types TypeScript corrects
- [ ] Variables d'environnement configurées

---

## **11. INTERACTIONS ENTRE COMPOSANTS - CARTOGRAPHIE COMPLÈTE**

### **11.1 Point d'Entrée - index.tsx**

**Fichier : index.tsx**
```typescript
IMPORTS :
├── React from 'react'
├── ReactDOM from 'react-dom/client'
├── App from './App'
└── './styles/glowing-card.css'

ACTIONS :
├── Trouve l'élément #root dans index.html
├── Crée une racine React 19
├── Rend <App /> en mode strict
└── Gère les erreurs si #root n'existe pas
```

**IMPACT** : Si ce fichier a un problème, tout le site plante.

### **11.2 Composant Racine - App.tsx**

**Fichier : App.tsx**
```typescript
IMPORTS :
├── React from 'react'
├── BrowserRouter, Routes, Route from 'react-router-dom'
├── Home from './pages/Home'
├── About from './pages/About'
├── Services from './pages/Services'
├── Portfolio from './pages/Portfolio'
├── Contact from './pages/Contact'
├── Header from './components/Header'
├── ScrollToTop from './components/ScrollToTop'
└── ScrollToTopButton from './components/ScrollToTopButton'

STRUCTURE :
BrowserRouter
├── ScrollToTop (auto-scroll sur changement de route)
├── Header (navigation fixe)
├── Routes
│   ├── Route "/" → Home
│   ├── Route "/about" → About
│   ├── Route "/services" → Services
│   ├── Route "/portfolio" → Portfolio
│   └── Route "/contact" → Contact
└── ScrollToTopButton (bouton de remontée)
```

**IMPACT** : Si une page importée n'existe pas, le routing plante.

### **11.3 Navigation - components/Header.tsx**

**Fichier : components/Header.tsx**
```typescript
IMPORTS :
├── React from 'react'
├── Link, useLocation from 'react-router-dom'

DÉPENDANCES :
├── Logo : /assets/02-Logo-FlexoDiv.png
├── Classes Tailwind : bg-brand-dark, text-brand-light, etc.
└── React Router pour navigation SPA

ÉTAT :
├── location.pathname pour lien actif
└── Classes conditionnelles selon la route

INTERACTIONS :
├── Link vers toutes les pages du site
├── Logo cliquable vers Home
└── Responsive avec menu mobile (si implémenté)
```

**IMPACT** : Si le logo n'existe pas ou si les routes changent, la navigation casse.

### **11.4 Pages Principales**

**1. pages/Home.tsx**
```typescript
IMPORTS :
├── React from 'react'
├── GlowingCard from '../components/GlowingCard' (si utilisé)

DÉPENDANCES :
├── Assets : Images hero, logos partenaires
├── Classes Tailwind pour layout
└── Possibles animations GSAP (à vérifier)
```

**2. pages/About.tsx**
```typescript
IMPORTS :
├── React from 'react'

DÉPENDANCES :
├── Assets : Photo de profil, CV
├── Classes Tailwind
└── Contenu statique principalement
```

**3. pages/Services.tsx**
```typescript
IMPORTS :
├── React from 'react'
├── GlowingCard from '../components/GlowingCard' (si utilisé)

DÉPENDANCES :
├── Assets : Icônes de services
├── Classes Tailwind
└── Données de services (hardcodées ou importées)
```

**4. pages/Portfolio.tsx**
```typescript
IMPORTS :
├── React from 'react'
├── GlowingCard from '../components/GlowingCard' (si utilisé)

DÉPENDANCES :
├── Assets : Screenshots de projets dans /public/assets/portfolio/
├── Classes Tailwind
├── Données de projets (fichiers .md dans /public/assets/portfolio/)
└── Liens vers GitHub, démos live
```

**5. pages/Contact.tsx ⭐ (CONTIENT L'AGENT GEMINI)**
```typescript
IMPORTS :
├── React from 'react'
└── AIChat from '../components/AIChat'

STRUCTURE :
├── Section titre et description
├── Grid 2 colonnes (lg:grid-cols-2)
│   ├── Colonne gauche : Formulaire de contact
│   └── Colonne droite : <AIChat />
└── Layout responsive

DÉPENDANCES CRITIQUES :
└── components/AIChat.tsx (AGENT GEMINI)
```

### **11.5 Composants Spécialisés**

**1. components/AIChat.tsx ⭐ (AGENT GEMINI)**
```typescript
IMPORTS :
├── React, { useState, useRef, useEffect } from 'react'
├── ChatMessage from '../types'
└── streamChatResponse from '../services/geminiService'

DÉPENDANCES CRITIQUES :
├── types/index.ts (interface ChatMessage)
├── services/geminiService.ts (logique IA)
└── Variable d'environnement VITE_GEMINI_API_KEY

ÉTAT GÉRÉ :
├── messages: ChatMessage[]
├── input: string
├── isLoading: boolean
└── chatContainerRef: useRef

INTERACTIONS :
├── Appelle streamChatResponse() pour chaque message
├── Met à jour l'UI en temps réel (streaming)
└── Gère l'auto-scroll du chat
```

**2. components/GlowingCard.tsx**
```typescript
IMPORTS :
├── React from 'react'

DÉPENDANCES :
├── styles/glowing-card.css (effets visuels)
├── Classes Tailwind
└── Props : title, description, children, etc.

UTILISATION :
├── Utilisé dans Services, Portfolio
└── Effets hover et animations CSS
```

**3. components/ScrollToTop.tsx**
```typescript
IMPORTS :
├── useEffect from 'react'
└── useLocation from 'react-router-dom'

FONCTION :
├── Écoute les changements de route
└── Scroll automatique vers le haut
```

**4. components/ScrollToTopButton.tsx**
```typescript
IMPORTS :
├── React, { useState, useEffect } from 'react'

ÉTAT :
├── isVisible: boolean (selon scroll position)

FONCTION :
├── Bouton flottant de remontée
├── Apparaît après scroll de X pixels
└── Smooth scroll vers le haut
```

### **11.6 Services et Logique Métier**

**services/geminiService.ts ⭐ (CŒUR DE L'IA)**
```typescript
IMPORTS :
├── GoogleGenAI, Chat from "@google/genai"
└── KNOWLEDGE_BASE from '../data/knowledge-base'

DÉPENDANCES CRITIQUES :
├── @google/genai (package npm)
├── data/knowledge-base.ts (données)
├── VITE_GEMINI_API_KEY (variable d'environnement)
└── Modèle gemini-2.5-flash

VARIABLES GLOBALES :
├── ai: GoogleGenAI
├── chat: Chat | null
└── messageCount: number

FONCTIONS :
├── createSystemInstruction() - Crée le prompt système
└── streamChatResponse() - Gère la conversation
```

### **11.7 Données et Configuration**

**data/knowledge-base.ts ⭐ (BASE DE CONNAISSANCES)**
```typescript
EXPORT :
└── KNOWLEDGE_BASE (objet complexe)

STRUCTURE :
├── profile - Infos personnelles Francisco
├── skills - Compétences techniques
├── stacks - Technologies recommandées
├── projects - Portfolio détaillé
├── services - Services proposés
├── assistance_packages - Tarifs PayPal
└── approach - Philosophie développement

UTILISATION :
└── Injectée dans l'instruction système Gemini
```

**types/index.ts**
```typescript
EXPORTS :
└── interface ChatMessage { sender: 'user' | 'ai'; text: string; }

UTILISATION :
└── Typé les messages dans AIChat.tsx
```

### **11.8 Configuration et Build**

**vite.config.ts**
```typescript
IMPORTS :
├── path from 'path'
└── defineConfig, loadEnv from 'vite'

CONFIGURATION :
├── Charge variables d'environnement
├── Expose process.env.GEMINI_API_KEY
└── Alias @ vers racine projet
```

**tailwind.config.js**
```typescript
CONFIGURATION :
├── Couleurs brand-* personnalisées
├── Breakpoints responsive
└── Purge CSS optimisée

COULEURS CRITIQUES :
├── brand-dark: #1B1C1D
├── brand-surface: #282A2C
├── brand-blue: #2190F6
├── brand-purple: #AE87F3
└── brand-muted: #9CA3AF
```

**package.json**
```json
DÉPENDANCES CRITIQUES :
├── @google/genai: ^1.9.0 (AGENT GEMINI)
├── react: ^19.1.0
├── react-router-dom: ^7.6.3
├── typescript: ~5.7.2
└── vite: ^6.2.0
```

**netlify.toml**
```toml
BUILD :
├── publish = "dist"
├── command = "npm run build"
└── NODE_VERSION = "18"

REDIRECTS :
└── /* → /index.html (SPA routing)
```

### **11.9 Points de Défaillance Critiques**

**1. Agent Gemini (PRIORITÉ MAXIMALE)**
- ❌ Si VITE_GEMINI_API_KEY manque → IA ne fonctionne pas
- ❌ Si @google/genai package corrompu → Crash de l'IA
- ❌ Si knowledge-base.ts malformé → Réponses incohérentes
- ❌ Si types/index.ts manque → Erreurs TypeScript

**2. Navigation et Routing**
- ❌ Si une page importée dans App.tsx manque → Crash routing
- ❌ Si react-router-dom corrompu → Site inaccessible
- ❌ Si Header.tsx casse → Navigation impossible

**3. Styles et Assets**
- ❌ Si Tailwind config corrompue → Styles cassés
- ❌ Si logo manque → Header cassé
- ❌ Si glowing-card.css manque → Effets visuels perdus

**4. Build et Déploiement**
- ❌ Si vite.config.ts malformé → Build impossible
- ❌ Si package.json corrompu → Dépendances cassées
- ❌ Si netlify.toml incorrect → Déploiement échoue

---

## **12. AGENT GEMINI - ARCHITECTURE DÉTAILLÉE**

### **12.1 Localisation et Structure**

**Page** : `pages/Contact.tsx`
**Section** : Colonne droite du layout 2 colonnes
**Composant principal** : `components/AIChat.tsx`

### **12.2 Chaîne de Dépendances Complète**

**1. Point d'Entrée - pages/Contact.tsx**
```typescript
// FICHIER : pages/Contact.tsx
// LIGNE 3 : import AIChat from '../components/AIChat';
// LIGNE 44 : <AIChat />

DÉPENDANCES :
├── React (import React from 'react')
├── components/AIChat.tsx (import AIChat)
└── Classes Tailwind CSS pour le layout

RESPONSABILITÉS :
├── Layout en grid 2 colonnes (lg:grid-cols-2)
├── Formulaire de contact classique (colonne gauche)
├── Assistant IA (colonne droite)
└── Responsive design mobile/desktop
```

**2. Interface Utilisateur - components/AIChat.tsx**
```typescript
// FICHIER : components/AIChat.tsx
// LIGNES CLÉS :
// L2-4 : Imports React hooks et types
// L4 : import { streamChatResponse } from '../services/geminiService'
// L68-105 : Fonction handleSend() - logique principale
// L107-159 : JSX de l'interface utilisateur

DÉPENDANCES :
├── React hooks (useState, useRef, useEffect)
├── types/index.ts (ChatMessage interface)
├── services/geminiService.ts (streamChatResponse function)
└── Icône SendIcon (SVG inline)

ÉTAT GÉRÉ :
├── messages: ChatMessage[] - Historique des messages
├── input: string - Texte en cours de saisie
├── isLoading: boolean - État de chargement
└── chatContainerRef - Référence pour auto-scroll

RESPONSABILITÉS :
├── Affichage des bulles de chat (user/ai)
├── Gestion de la saisie utilisateur
├── Appel du service Gemini
├── Streaming des réponses en temps réel
├── Auto-scroll vers le bas
└── Gestion des états de chargement
```

**3. Logique Métier - services/geminiService.ts**
```typescript
// FICHIER : services/geminiService.ts
// LIGNES CLÉS :
// L2-3 : Imports Google Gemini SDK et base de connaissances
// L6 : const API_KEY = import.meta.env.VITE_GEMINI_API_KEY
// L18-50 : createSystemInstruction() - Prompt système
// L52-120 : streamChatResponse() - Fonction principale

DÉPENDANCES :
├── @google/genai (GoogleGenAI, Chat)
├── data/knowledge-base.ts (KNOWLEDGE_BASE)
└── Variable d'environnement VITE_GEMINI_API_KEY

VARIABLES GLOBALES :
├── ai: GoogleGenAI - Instance API
├── chat: Chat | null - Session de conversation
└── messageCount: number - Compteur de messages

RESPONSABILITÉS :
├── Configuration de l'API Gemini
├── Création de l'instruction système enrichie
├── Gestion de la session de chat
├── Streaming des réponses
├── Limitation à 50 messages par session
└── Gestion des erreurs API
```

**4. Base de Connaissances - data/knowledge-base.ts**
```typescript
// FICHIER : data/knowledge-base.ts
// STRUCTURE : Objet KNOWLEDGE_BASE exporté
// LIGNES 6-235 : Données complètes sur Francisco/FlexoDiv

CONTENU :
├── profile (L8-17) - Informations personnelles
├── skills (L20-68) - Compétences techniques détaillées
├── stacks (L71-86) - Stacks techniques recommandées
├── projects (L89-146) - Portfolio de projets
├── services (L149-180) - Services proposés
├── assistance_packages (L183-215) - Packages avec tarifs PayPal
└── approach (L218-234) - Philosophie de développement

UTILISATION :
└── Injectée dans l'instruction système de Gemini
```

### **12.3 Configuration et Variables**

**Variables d'Environnement :**
```bash
# FICHIER : .env.local (NON COMMITÉ)
VITE_GEMINI_API_KEY=AIzaSyCqooMDW5sKHciKAX19JUolQT01RDhBysc

# FICHIER : vite.config.ts
# Configuration pour exposer les variables d'environnement
define: {
  'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
}
```

**Types TypeScript :**
```typescript
// FICHIER : types/index.ts
export interface ChatMessage {
  sender: 'user' | 'ai';
  text: string;
}
```

### **12.4 Flux de Données**

**1. Interaction Utilisateur :**
```
Utilisateur tape message
↓
onChange met à jour input state
↓
Utilisateur appuie sur Entrée ou clique Envoyer
↓
handleSend() est appelée
```

**2. Traitement du Message :**
```
handleSend()
├── Ajoute message utilisateur à messages[]
├── Vide l'input
├── Active isLoading
├── Ajoute message IA vide
└── Appelle streamChatResponse()
```

**3. Streaming de la Réponse :**
```
streamChatResponse()
├── Crée/récupère session chat Gemini
├── Envoie message avec instruction système
├── Stream la réponse token par token
├── Met à jour le dernier message IA
└── Désactive isLoading
```

### **12.5 Points Critiques**

**Sécurité :**
- ✅ Clé API dans .env.local (non commité)
- ✅ Validation côté client des inputs
- ⚠️ Pas de validation côté serveur (frontend only)

**Performance :**
- ✅ Streaming des réponses (UX fluide)
- ✅ Limitation à 50 messages par session
- ⚠️ Pas de cache des réponses

**Maintenance :**
- ✅ Base de connaissances centralisée
- ✅ Types TypeScript stricts
- ✅ Séparation claire des responsabilités

**Déploiement :**
- ✅ Variable d'environnement configurée sur Netlify
- ✅ Build automatique avec Vite
- ✅ Compatible avec le routing React Router
