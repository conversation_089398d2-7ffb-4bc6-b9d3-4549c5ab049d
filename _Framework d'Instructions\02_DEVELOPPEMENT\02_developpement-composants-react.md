# 02_developpement-composants-react.md : Développement des Composants React

**Objectif :** Ce document guide l'agent de codage dans l'implémentation des composants React conformément à la structure et aux conventions définies.

**Directives pour l'agent de codage :**

1.  **Respect de la structure :**
    *   Tous les nouveaux composants doivent être créés et organisés selon les principes de la conception atomique (Atomic Design) ou la segmentation Smart/Dumb components, comme décrit dans le document `01_ARCHITECTURE_ET_SETUP/02_structure-composants-react.md`.
    *   Assurez-vous que chaque composant réside dans son propre dossier, contenant le fichier `.tsx` (ou `.ts` si le composant n'a pas de JSX), le fichier de style associé (si Tailwind ne suffit pas), et potentiellement un fichier de test.
2.  **Conventions de nommage :** Suivez les conventions de nommage React (PascalCase pour les composants, camelCase pour les fonctions et variables).
3.  **Props et typage :**
    *   Définissez clairement les types des props en utilisant TypeScript pour tous les composants.
    *   Privilégiez la déstructuration des props pour une meilleure lisibilité.
4.  **Gestion de l'état :**
    *   Utilisez les hooks d'état (`useState`, `useReducer`) de manière appropriée pour gérer l'état local des composants.
    *   Pour l'état global ou partagé, préparez-vous à utiliser un gestionnaire d'état que nous pourrions définir ultérieurement (ex: Context API, Redux, Zustand).
5.  **Effets de bord :** Gérez les effets de bord (appels API, abonnements, manipulations du DOM) à l'aide du hook `useEffect`, en veillant à nettoyer les ressources pour éviter les fuites de mémoire.
6.  **Optimisation des performances :**
    *   Utilisez `React.memo`, `useCallback`, `useMemo` lorsque c'est nécessaire pour optimiser le rendu des composants et éviter les re-rendus inutiles.
    *   Soyez attentif à la complexité des calculs au sein des rendus et déportez-les si possible.
7.  **Styles Tailwind CSS :**
    *   Appliquez le stylisme autant que possible via les classes utilitaires de Tailwind CSS, en vous référant à la configuration dans `01_ARCHITECTURE_ET_SETUP/03_configuration-tailwind.md`.
    *   N'utilisez des feuilles de style CSS modulaires ou des fichiers `.css` classiques qu'en dernier recours, pour des styles très spécifiques ou complexes non gérables par Tailwind.
8.  **Accessibilité (A11y) :** Intégrez les meilleures pratiques d'accessibilité dans la conception et l'implémentation des composants (ex: sémantique HTML, attributs `aria-`, gestion du focus).
9.  **Tests :**
    *   Écrivez des tests unitaires pour chaque composant, en utilisant des frameworks comme Jest ou React Testing Library.
    *   Assurez-vous de tester les comportements par défaut, les états et les interactions avec les composants.